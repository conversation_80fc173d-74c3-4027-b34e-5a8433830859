/**
 * 서비스 메서드 테스트 스크립트
 * 
 * 수정된 서비스들의 메서드가 올바르게 정의되고 작동하는지 확인합니다.
 */

const FestivalService = require('./src/services/festivalService');
const ExhibitionService = require('./src/services/exhibitionService');
const WelfareService = require('./src/services/welfareService');

async function testServiceMethods() {
  console.log('=== 서비스 메서드 테스트 시작 ===\n');

  try {
    // 1. FestivalService 테스트
    console.log('1. FestivalService 메서드 테스트');
    const festivalService = new FestivalService();
    
    // 메서드 존재 확인
    console.log('- transformApiDataToDbFormat 메서드 존재:', typeof festivalService.transformApiDataToDbFormat === 'function');
    console.log('- getExistingFestivals 메서드 존재:', typeof festivalService.getExistingFestivals === 'function');
    console.log('- syncFestivals 메서드 존재:', typeof festivalService.syncFestivals === 'function');
    
    // transformApiDataToDbFormat 메서드 테스트
    const mockApiData = [
      { seq: 'test1', title: '테스트 축제 1', contents: '내용1' },
      { seq: 'test2', title: '테스트 축제 2', contents: '내용2' }
    ];
    
    const mockExistingData = [
      { original_api_id: 'test1', is_exposed: true, admin_memo: '기존 메모' }
    ];
    
    const transformedData = festivalService.transformApiDataToDbFormat(mockApiData, mockExistingData);
    console.log('- 변환된 데이터 개수:', transformedData.length);
    console.log('- 기존 데이터 is_exposed 유지:', transformedData[0].is_exposed === true);
    console.log('- 새 데이터 is_exposed 기본값:', transformedData[1].is_exposed === false);
    console.log('- 실제 변환된 데이터:', JSON.stringify(transformedData.map(item => ({
      original_api_id: item.original_api_id,
      is_exposed: item.is_exposed,
      admin_memo: item.admin_memo
    })), null, 2));
    
    console.log('');

    // 2. ExhibitionService 테스트
    console.log('2. ExhibitionService 메서드 테스트');
    const exhibitionService = new ExhibitionService();
    
    // 메서드 존재 확인
    console.log('- transformApiDataToDbFormat 메서드 존재:', typeof exhibitionService.transformApiDataToDbFormat === 'function');
    console.log('- getExistingExhibitions 메서드 존재:', typeof exhibitionService.getExistingExhibitions === 'function');
    console.log('- syncExhibitions 메서드 존재:', typeof exhibitionService.syncExhibitions === 'function');
    
    // transformApiDataToDbFormat 메서드 테스트
    const mockExhibitionApiData = [
      { seq: 'ex_test1', title: '테스트 전시 1', categoryName: '문화' },
      { seq: 'ex_test2', title: '테스트 전시 2', categoryName: '예술' }
    ];
    
    const mockExistingExhibitionData = [
      { original_api_id: 'ex_test1', is_exposed: true, admin_memo: '전시 메모' }
    ];
    
    const transformedExhibitionData = exhibitionService.transformApiDataToDbFormat(mockExhibitionApiData, mockExistingExhibitionData);
    console.log('- 변환된 데이터 개수:', transformedExhibitionData.length);
    console.log('- 기존 데이터 is_exposed 유지:', transformedExhibitionData[0].is_exposed === true);
    console.log('- 새 데이터 is_exposed 기본값:', transformedExhibitionData[1].is_exposed === false);
    
    console.log('');

    // 3. WelfareService 테스트
    console.log('3. WelfareService 메서드 테스트');
    const welfareService = new WelfareService();
    
    // 메서드 존재 확인
    console.log('- transformApiDataToDbFormat 메서드 존재:', typeof welfareService.transformApiDataToDbFormat === 'function');
    console.log('- getExistingWelfareServices 메서드 존재:', typeof welfareService.getExistingWelfareServices === 'function');
    console.log('- syncWelfareServices 메서드 존재:', typeof welfareService.syncWelfareServices === 'function');
    
    // transformApiDataToDbFormat 메서드 테스트
    const mockWelfareApiData = [
      { seq: 'wf_test1', name: '테스트 복지 1', allLoc: true },
      { seq: 'wf_test2', name: '테스트 복지 2', jejuLoc: true }
    ];
    
    const mockExistingWelfareData = [
      { original_api_id: 'wf_test1', is_exposed: true, admin_memo: '복지 메모' }
    ];
    
    const transformedWelfareData = welfareService.transformApiDataToDbFormat(mockWelfareApiData, mockExistingWelfareData);
    console.log('- 변환된 데이터 개수:', transformedWelfareData.length);
    console.log('- 기존 데이터 is_exposed 유지:', transformedWelfareData[0].is_exposed === true);
    console.log('- 새 데이터 is_exposed 기본값:', transformedWelfareData[1].is_exposed === false);
    
    console.log('');

    // 4. 스케줄러 통합 테스트
    console.log('4. 스케줄러 통합 테스트');
    const AllDataScheduler = require('./src/scheduler/allDataScheduler');
    const scheduler = new AllDataScheduler();
    
    console.log('- AllDataScheduler 인스턴스 생성 성공:', scheduler instanceof AllDataScheduler);
    console.log('- festivalService 인스턴스 존재:', scheduler.festivalService instanceof FestivalService);
    console.log('- exhibitionService 인스턴스 존재:', scheduler.exhibitionService instanceof ExhibitionService);
    console.log('- welfareService 인스턴스 존재:', scheduler.welfareService instanceof WelfareService);
    
    // 각 서비스의 수정된 메서드들이 스케줄러에서 접근 가능한지 확인
    console.log('- festivalService.getExistingFestivals 메서드 접근 가능:', typeof scheduler.festivalService.getExistingFestivals === 'function');
    console.log('- exhibitionService.getExistingExhibitions 메서드 접근 가능:', typeof scheduler.exhibitionService.getExistingExhibitions === 'function');
    console.log('- welfareService.getExistingWelfareServices 메서드 접근 가능:', typeof scheduler.welfareService.getExistingWelfareServices === 'function');

    console.log('\n=== 서비스 메서드 테스트 완료 ===');
    console.log('✅ 모든 서비스의 메서드가 올바르게 정의되었습니다.');
    console.log('✅ is_exposed 값 유지 로직이 올바르게 구현되었습니다.');
    console.log('✅ 스케줄러와의 통합이 정상적으로 작동합니다.');

  } catch (error) {
    console.error('서비스 메서드 테스트 중 오류 발생:', error.message);
    console.error('스택 추적:', error.stack);
  }
}

// 테스트 실행
if (require.main === module) {
  testServiceMethods();
}

module.exports = { testServiceMethods };
