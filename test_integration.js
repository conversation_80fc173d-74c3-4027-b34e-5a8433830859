/**
 * 통합 테스트 스크립트
 * 
 * 수정된 서비스들이 실제 스케줄러와 함께 올바르게 작동하는지 테스트합니다.
 * is_exposed 값이 유지되는지 확인합니다.
 */

const AllDataScheduler = require('./src/scheduler/allDataScheduler');
const { supabase } = require('./src/config/database');

async function testIntegration() {
  console.log('=== 통합 테스트 시작 ===\n');

  try {
    // 1. 테스트용 데이터 준비 (기존 데이터 시뮬레이션)
    console.log('1. 테스트용 기존 데이터 준비 중...');
    
    // 축제 테스트 데이터 삽입
    const testFestivalData = {
      original_api_id: 'test_festival_integration',
      title: '통합 테스트 축제',
      content_html: '테스트 내용',
      is_exposed: true,
      admin_memo: '테스트용 메모',
      fetched_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { error: festivalError } = await supabase
      .from('festivals')
      .upsert(testFestivalData, { onConflict: 'original_api_id' });

    if (festivalError) {
      console.error('축제 테스트 데이터 삽입 실패:', festivalError);
    } else {
      console.log('✅ 축제 테스트 데이터 삽입 완료');
    }

    // 전시회 테스트 데이터 삽입
    const testExhibitionData = {
      original_api_id: 'test_exhibition_integration',
      title: '통합 테스트 전시회',
      category_name: '테스트 카테고리',
      is_exposed: true,
      admin_memo: '전시회 테스트 메모',
      fetched_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { error: exhibitionError } = await supabase
      .from('exhibitions')
      .upsert(testExhibitionData, { onConflict: 'original_api_id' });

    if (exhibitionError) {
      console.error('전시회 테스트 데이터 삽입 실패:', exhibitionError);
    } else {
      console.log('✅ 전시회 테스트 데이터 삽입 완료');
    }

    // 복지서비스 테스트 데이터 삽입
    const testWelfareData = {
      original_api_id: 'test_welfare_integration',
      service_name: '통합 테스트 복지서비스',
      is_all_location: true,
      is_exposed: true,
      admin_memo: '복지 테스트 메모',
      fetched_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { error: welfareError } = await supabase
      .from('welfare_services')
      .upsert(testWelfareData, { onConflict: 'original_api_id' });

    if (welfareError) {
      console.error('복지서비스 테스트 데이터 삽입 실패:', welfareError);
    } else {
      console.log('✅ 복지서비스 테스트 데이터 삽입 완료');
    }

    console.log('\n2. 데이터 동기화 전 상태 확인...');
    
    // 동기화 전 데이터 상태 확인
    const beforeFestival = await supabase
      .from('festivals')
      .select('original_api_id, is_exposed, admin_memo')
      .eq('original_api_id', 'test_festival_integration')
      .single();

    const beforeExhibition = await supabase
      .from('exhibitions')
      .select('original_api_id, is_exposed, admin_memo')
      .eq('original_api_id', 'test_exhibition_integration')
      .single();

    const beforeWelfare = await supabase
      .from('welfare_services')
      .select('original_api_id, is_exposed, admin_memo')
      .eq('original_api_id', 'test_welfare_integration')
      .single();

    console.log('동기화 전 축제 데이터:', beforeFestival.data);
    console.log('동기화 전 전시회 데이터:', beforeExhibition.data);
    console.log('동기화 전 복지 데이터:', beforeWelfare.data);

    console.log('\n3. 스케줄러를 통한 데이터 동기화 실행...');
    
    // 스케줄러 인스턴스 생성 및 실행
    const scheduler = new AllDataScheduler();
    
    // 실제 API 호출 및 데이터 동기화 실행
    // 주의: 실제 API를 호출하므로 시간이 걸릴 수 있습니다
    console.log('실제 API 호출 및 데이터 동기화 중... (시간이 걸릴 수 있습니다)');
    const result = await scheduler.runOnce();
    
    console.log('동기화 결과:', result);

    console.log('\n4. 데이터 동기화 후 상태 확인...');
    
    // 동기화 후 데이터 상태 확인
    const afterFestival = await supabase
      .from('festivals')
      .select('original_api_id, is_exposed, admin_memo, updated_at')
      .eq('original_api_id', 'test_festival_integration')
      .single();

    const afterExhibition = await supabase
      .from('exhibitions')
      .select('original_api_id, is_exposed, admin_memo, updated_at')
      .eq('original_api_id', 'test_exhibition_integration')
      .single();

    const afterWelfare = await supabase
      .from('welfare_services')
      .select('original_api_id, is_exposed, admin_memo, updated_at')
      .eq('original_api_id', 'test_welfare_integration')
      .single();

    console.log('동기화 후 축제 데이터:', afterFestival.data);
    console.log('동기화 후 전시회 데이터:', afterExhibition.data);
    console.log('동기화 후 복지 데이터:', afterWelfare.data);

    console.log('\n5. 결과 검증...');
    
    // 결과 검증
    let allTestsPassed = true;

    // 축제 데이터 검증
    if (afterFestival.data && afterFestival.data.is_exposed === true && afterFestival.data.admin_memo === '테스트용 메모') {
      console.log('✅ 축제 데이터의 is_exposed 값이 올바르게 유지됨');
    } else {
      console.log('❌ 축제 데이터의 is_exposed 값이 유지되지 않음');
      allTestsPassed = false;
    }

    // 전시회 데이터 검증
    if (afterExhibition.data && afterExhibition.data.is_exposed === true && afterExhibition.data.admin_memo === '전시회 테스트 메모') {
      console.log('✅ 전시회 데이터의 is_exposed 값이 올바르게 유지됨');
    } else {
      console.log('❌ 전시회 데이터의 is_exposed 값이 유지되지 않음');
      allTestsPassed = false;
    }

    // 복지 데이터 검증
    if (afterWelfare.data && afterWelfare.data.is_exposed === true && afterWelfare.data.admin_memo === '복지 테스트 메모') {
      console.log('✅ 복지 데이터의 is_exposed 값이 올바르게 유지됨');
    } else {
      console.log('❌ 복지 데이터의 is_exposed 값이 유지되지 않음');
      allTestsPassed = false;
    }

    console.log('\n6. 테스트 데이터 정리...');
    
    // 테스트 데이터 삭제
    await supabase.from('festivals').delete().eq('original_api_id', 'test_festival_integration');
    await supabase.from('exhibitions').delete().eq('original_api_id', 'test_exhibition_integration');
    await supabase.from('welfare_services').delete().eq('original_api_id', 'test_welfare_integration');
    
    console.log('✅ 테스트 데이터 정리 완료');

    console.log('\n=== 통합 테스트 결과 ===');
    if (allTestsPassed) {
      console.log('🎉 모든 테스트가 성공적으로 통과했습니다!');
      console.log('is_exposed 컬럼 값이 올바르게 유지됩니다.');
    } else {
      console.log('⚠️  일부 테스트가 실패했습니다. 코드를 다시 확인해주세요.');
    }

  } catch (error) {
    console.error('통합 테스트 중 오류 발생:', error.message);
    console.error('스택 추적:', error.stack);
  }
}

// 테스트 실행
if (require.main === module) {
  testIntegration();
}

module.exports = { testIntegration };
