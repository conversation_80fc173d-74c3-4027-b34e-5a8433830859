const axios = require('axios');
const { supabase } = require('../config/database');

// API 설정 상수
const JEJU_FESTIVAL_API_URL = 'https://www.jeju.go.kr/api/jejutoseoul/festival/';

class FestivalService {
  constructor() {
    this.apiUrl = JEJU_FESTIVAL_API_URL;
  }

  /**
   * 제주도 축제/행사 API에서 데이터를 가져옵니다.
   */
  async fetchFestivalData() {
    try {
      console.log('제주도 축제/행사 API 호출 중...');
      
      const response = await axios.get(this.apiUrl, {
        timeout: 30000 // 30초 타임아웃
      });

      if (response.data.error) {
        throw new Error(`API 호출 실패: ${response.data.error}`);
      }

      console.log(`API 응답 성공: ${response.data.total}개의 축제/행사 정보 수신`);
      return response.data.items || [];
    } catch (error) {
      console.error('API 호출 중 오류 발생:', error.message);
      throw error;
    }
  }

  /**
   * API 데이터를 DB 스키마에 맞게 변환합니다.
   * @param {Array} apiData - API에서 받은 원본 데이터
   * @param {Array} existingData - 기존 DB에 있는 데이터 (is_exposed 값 유지용)
   */
  transformApiDataToDbFormat(apiData, existingData = []) {
    const currentTime = new Date().toISOString();

    // 기존 데이터를 original_api_id로 매핑
    const existingDataMap = new Map();
    existingData.forEach(item => {
      if (item.original_api_id) {
        existingDataMap.set(item.original_api_id, item);
      }
    });

    return apiData.map(item => {
      const originalApiId = item.seq ? item.seq.toString() : null;
      const existingItem = existingDataMap.get(originalApiId);

      return {
        original_api_id: originalApiId,
        title: item.title || '',
        content_html: item.contents || null,
        source_url: item.url || null,
        writer_name: item.writer || null,
        written_date: item.writeDate ? new Date(item.writeDate).toISOString() : null,
        files_info: item.files ? JSON.stringify(item.files) : null,
        api_raw_data: item,
        // 기존 데이터가 있으면 is_exposed 값을 유지, 없으면 false로 설정
        is_exposed: existingItem ? existingItem.is_exposed : false,
        admin_memo: existingItem ? existingItem.admin_memo : null,
        fetched_at: currentTime,
        updated_at: currentTime
      };
    });
  }

  /**
   * 여러 축제 데이터를 배치로 upsert합니다.
   */
  async batchUpsertFestivals(festivalsData) {
    try {
      console.log(`${festivalsData.length}개의 축제/행사 데이터 저장 중...`);
      
      // upsert 시 updated_at을 현재 시간으로 업데이트
      const festivalsWithUpdatedTime = festivalsData.map(item => ({
        ...item,
        updated_at: new Date().toISOString()
      }));
      
      const { data, error } = await supabase
        .from('festivals')
        .upsert(festivalsWithUpdatedTime, {
          onConflict: 'original_api_id'
        });

      if (error) {
        throw error;
      }

      console.log(`${festivalsData.length}개의 축제/행사 데이터 저장 완료`);
      return data;
    } catch (error) {
      console.error('배치 데이터 저장 실패:', error.message);
      throw error;
    }
  }

  /**
   * 기존 축제 데이터를 조회합니다.
   */
  async getExistingFestivals() {
    try {
      const { data, error } = await supabase
        .from('festivals')
        .select('original_api_id, is_exposed, admin_memo');

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('기존 축제 데이터 조회 실패:', error.message);
      return []; // 조회 실패 시 빈 배열 반환하여 새 데이터로 처리
    }
  }

  /**
   * 전체 프로세스를 실행합니다: API 호출 → 기존 데이터 조회 → 데이터 변환 → DB 저장
   */
  async syncFestivals() {
    try {
      console.log('=== 축제/행사 데이터 동기화 시작 ===');
      const startTime = new Date();

      // 1. API에서 데이터 가져오기
      const apiData = await this.fetchFestivalData();

      if (!apiData || apiData.length === 0) {
        console.log('API에서 받은 데이터가 없습니다.');
        return;
      }

      // 2. 기존 데이터 조회 (is_exposed 값 유지용)
      console.log('기존 축제 데이터 조회 중...');
      const existingData = await this.getExistingFestivals();
      console.log(`기존 데이터 ${existingData.length}개 조회 완료`);

      // 3. 데이터 변환 (기존 is_exposed 값 유지)
      const dbData = this.transformApiDataToDbFormat(apiData, existingData);

      // 4. DB에 저장
      await this.batchUpsertFestivals(dbData);

      const endTime = new Date();
      const duration = (endTime - startTime) / 1000;

      console.log(`=== 축제/행사 데이터 동기화 완료 (소요시간: ${duration}초) ===`);

      return {
        success: true,
        processedCount: dbData.length,
        duration: duration
      };
    } catch (error) {
      console.error('축제/행사 데이터 동기화 실패:', error.message);
      throw error;
    }
  }
}

module.exports = FestivalService;