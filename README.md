# 제주도 통합 데이터 수집 스케줄러 (GRAP API)

제주도의 다양한 공공 데이터를 정기적으로 수집하여 Supabase 데이터베이스에 저장하는 Node.js 기반 통합 데이터 수집 시스템입니다.

## 📋 프로젝트 개요

이 프로젝트는 제주도의 4가지 주요 공공 데이터를 자동으로 수집하고 관리하는 통합 스케줄러입니다:

1. **주유소 가격 정보** - 제주도 내 주유소별 실시간 유가 정보
2. **축제/행사 정보** - 제주도에서 개최되는 각종 축제 및 행사 정보
3. **전시회 정보** - 제주도 내 문화 전시회 및 갤러리 정보
4. **복지서비스 정보** - 제주도민을 위한 각종 복지 서비스 정보

## 🏗️ 시스템 아키텍처

### 전체 구조
```
grap_api/
├── src/                          # 소스 코드 디렉토리
│   ├── config/                   # 설정 파일
│   │   └── database.js          # Supabase 데이터베이스 설정
│   ├── scheduler/               # 스케줄러 관련
│   │   ├── allDataScheduler.js  # 통합 스케줄러 (메인)
│   │   └── gasPriceScheduler.js # 주유소 전용 스케줄러 (레거시)
│   └── services/                # 데이터 수집 서비스
│       ├── gasPriceService.js   # 주유소 가격 수집
│       ├── festivalService.js   # 축제/행사 수집
│       ├── exhibitionService.js # 전시회 수집
│       └── welfareService.js    # 복지서비스 수집
├── web.js                       # Cafe24 호스팅용 메인 파일
├── index.js                     # 로컬 개발용 메인 파일
├── test-scheduler.js            # 스케줄러 테스트 파일
├── debug-api.js                 # API 디버깅 도구
├── check-date-format.js         # 날짜 형식 확인 도구
└── package.json                 # 프로젝트 설정 및 의존성
```

### 아키텍처 특징

#### 1. 모듈화된 서비스 구조
- **Service Layer**: 각 데이터 소스별로 독립적인 서비스 클래스
- **Scheduler Layer**: 통합 스케줄러가 모든 서비스를 조율
- **Config Layer**: 중앙화된 설정 관리

#### 2. 이중 실행 환경 지원
- **프로덕션 환경**: `web.js` (Cafe24 호스팅, HTTP 서버 포함)
- **개발 환경**: `index.js` (로컬 실행, CLI 기반)

#### 3. 강력한 에러 처리
- 서비스별 독립적 에러 처리
- 하나의 서비스 실패가 전체 시스템에 영향을 주지 않음
- 상세한 로깅 및 에러 추적

## 🔧 기술 스택

### 핵심 기술
- **Runtime**: Node.js 14.x
- **Database**: Supabase (PostgreSQL)
- **Scheduler**: node-cron
- **HTTP Client**: axios
- **Web Framework**: Express.js (프로덕션 환경)

### 주요 의존성
```json
{
  "@supabase/supabase-js": "^1.35.7",  // Supabase 클라이언트
  "axios": "^0.27.2",                  // HTTP 요청
  "express": "^4.21.2",                // 웹 서버 (Cafe24용)
  "node-cron": "^3.0.2",               // 스케줄링
  "xml2js": "^0.6.2"                   // XML 파싱 (일부 API용)
}
```

## 📊 데이터 수집 상세

### 1. 주유소 가격 정보 (GasPriceService)
- **API**: `http://api.jejuits.go.kr/api/infoGasPriceList`
- **수집 데이터**: 휘발유, 고급휘발유, 경유, LPG 가격
- **업데이트 방식**: opinet_id + price_date 기준 upsert
- **특징**: 실시간 가격 정보, 일별 가격 히스토리 관리

### 2. 축제/행사 정보 (FestivalService)
- **API**: `https://www.jeju.go.kr/api/jejutoseoul/festival/`
- **수집 데이터**: 축제명, 내용, 작성자, 첨부파일 정보
- **업데이트 방식**: original_api_id 기준 upsert
- **특징**: HTML 콘텐츠 저장, 파일 정보 JSON 저장

### 3. 전시회 정보 (ExhibitionService)
- **API**: `http://www.jeju.go.kr/rest/JejuExhibitionService/getJejucultureExhibitionList`
- **수집 데이터**: 전시회명, 카테고리, 기간, 장소, 주최자 정보
- **업데이트 방식**: original_api_id 기준 upsert
- **특징**: 페이지네이션 지원, 배치 처리, 날짜 검증

### 4. 복지서비스 정보 (WelfareService)
- **API**: `http://www.jeju.go.kr/rest/JejuWelfareServiceInfo/getJejuWelfareServiceInfoList`
- **수집 데이터**: 서비스명, 지역별 제공 여부, 지원 대상, 신청 방법
- **업데이트 방식**: original_api_id 기준 upsert
- **특징**: 지역별 서비스 제공 여부 플래그 관리

## ⚙️ 설정 및 설치

### 1. 의존성 설치
```bash
npm install
```

### 2. 설정 구조

모든 설정값이 코드에 하드코딩되어 있어 별도 환경 설정이 불필요합니다.

#### 주요 설정 파일:
```javascript
// src/config/database.js - Supabase 설정
const SUPABASE_URL = 'https://effgsrxotxfhpiczalrq.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';

// src/scheduler/allDataScheduler.js - 스케줄 설정
const SCHEDULE_CRON = '5 2,8,14,20 * * *'; // 새벽 2시 5분 + 6시간마다

// web.js - 서버 설정
const PORT = 8001; // Cafe24 기본 포트
```

## 🚀 실행 방법

### Cafe24 호스팅 (프로덕션)
```bash
npm start  # web.js 실행 - HTTP 서버 + 스케줄러
```

### 로컬 개발 환경

#### 스케줄러 모드 (지속 실행)
```bash
npm run local    # 또는 npm run dev
```

#### 한 번만 실행 (테스트용)
```bash
node index.js --run-once
# 또는
node index.js -o
```

#### 시작 시 즉시 실행 후 스케줄러 시작
```bash
node index.js --run-on-start
# 또는
node index.js -s
```

#### 개별 테스트 실행
```bash
node test-scheduler.js     # 통합 스케줄러 테스트
node debug-api.js          # API 응답 디버깅
node check-date-format.js  # 날짜 형식 확인
```

## 📅 스케줄링 시스템

### 기본 스케줄
- **실행 시간**: 새벽 2시 5분, 오전 8시 5분, 오후 2시 5분, 오후 8시 5분
- **Cron 표현식**: `5 2,8,14,20 * * *`
- **실행 순서**: 주유소 → 축제/행사 → 전시회 → 복지서비스

### 스케줄 커스터마이징
```javascript
// src/scheduler/allDataScheduler.js에서 수정
const SCHEDULE_CRON = '5 2,8,14,20 * * *';  // 현재 설정
// const SCHEDULE_CRON = '0 */6 * * *';      // 6시간마다
// const SCHEDULE_CRON = '0 */1 * * *';      // 1시간마다
// const SCHEDULE_CRON = '0 0 * * *';        // 매일 자정
```

### 동시 실행 방지
- `isRunning` 플래그로 중복 실행 방지
- 이전 작업이 완료되지 않으면 다음 스케줄 건너뛰기

## 🗄️ 데이터베이스 스키마

### 1. gas_prices 테이블 (주유소 가격)
```sql
CREATE TABLE gas_prices (
  id SERIAL PRIMARY KEY,
  opinet_id VARCHAR NOT NULL,              -- 주유소 고유 ID
  gasoline_price INTEGER DEFAULT 0,        -- 휘발유 가격
  premium_gasoline_price INTEGER DEFAULT 0, -- 고급휘발유 가격
  diesel_price INTEGER DEFAULT 0,          -- 경유 가격
  lpg_price INTEGER DEFAULT 0,             -- LPG 가격
  price_date DATE NOT NULL,                -- 가격 기준일
  api_raw_data TEXT,                       -- 원본 API 응답 JSON
  fetched_at TIMESTAMP DEFAULT NOW(),      -- 수집 시간
  created_at TIMESTAMP DEFAULT NOW(),      -- 생성 시간
  updated_at TIMESTAMP DEFAULT NOW(),      -- 수정 시간
  UNIQUE(opinet_id, price_date)            -- 복합 유니크 키
);
```

### 2. festivals 테이블 (축제/행사)
```sql
CREATE TABLE festivals (
  id SERIAL PRIMARY KEY,
  original_api_id VARCHAR UNIQUE,          -- API 원본 ID
  title VARCHAR NOT NULL,                  -- 축제/행사명
  content_html TEXT,                       -- 내용 (HTML)
  source_url VARCHAR,                      -- 원본 URL
  writer_name VARCHAR,                     -- 작성자
  written_date TIMESTAMP,                 -- 작성일
  files_info JSONB,                       -- 첨부파일 정보 JSON
  api_raw_data JSONB,                     -- 원본 API 응답
  is_exposed BOOLEAN DEFAULT FALSE,        -- 노출 여부
  admin_memo TEXT,                        -- 관리자 메모
  fetched_at TIMESTAMP DEFAULT NOW(),      -- 수집 시간
  updated_at TIMESTAMP DEFAULT NOW()       -- 수정 시간
);
```

### 3. exhibitions 테이블 (전시회)
```sql
CREATE TABLE exhibitions (
  id SERIAL PRIMARY KEY,
  original_api_id VARCHAR UNIQUE,          -- API 원본 ID
  title VARCHAR,                          -- 전시회명
  category_name VARCHAR,                  -- 카테고리
  cover_image_url VARCHAR,                -- 커버 이미지 URL
  start_date TIMESTAMP,                   -- 시작일
  end_date TIMESTAMP,                     -- 종료일
  time_info VARCHAR,                      -- 시간 정보
  pay_info VARCHAR,                       -- 요금 정보
  location_name VARCHAR,                  -- 장소명
  organizer_info VARCHAR,                 -- 주최자 정보
  tel_number VARCHAR,                     -- 연락처
  status_info VARCHAR,                    -- 상태 정보
  division_name VARCHAR,                  -- 구분명
  api_raw_data JSONB,                     -- 원본 API 응답
  is_exposed BOOLEAN DEFAULT FALSE,        -- 노출 여부
  admin_memo TEXT,                        -- 관리자 메모
  fetched_at TIMESTAMP DEFAULT NOW(),      -- 수집 시간
  updated_at TIMESTAMP DEFAULT NOW()       -- 수정 시간
);
```

### 4. welfare_services 테이블 (복지서비스)
```sql
CREATE TABLE welfare_services (
  id SERIAL PRIMARY KEY,
  original_api_id VARCHAR UNIQUE,          -- API 원본 ID
  service_name VARCHAR NOT NULL,           -- 서비스명
  is_all_location BOOLEAN DEFAULT FALSE,   -- 전체 지역 제공 여부
  is_jeju_location BOOLEAN DEFAULT FALSE,  -- 제주시 제공 여부
  is_seogwipo_location BOOLEAN DEFAULT FALSE, -- 서귀포시 제공 여부
  support_target_html TEXT,               -- 지원 대상 (HTML)
  support_content_html TEXT,              -- 지원 내용 (HTML)
  application_info_html TEXT,             -- 신청 방법 (HTML)
  api_raw_data JSONB,                     -- 원본 API 응답
  is_exposed BOOLEAN DEFAULT FALSE,        -- 노출 여부
  admin_memo TEXT,                        -- 관리자 메모
  fetched_at TIMESTAMP DEFAULT NOW(),      -- 수집 시간
  updated_at TIMESTAMP DEFAULT NOW()       -- 수정 시간
);
```

## 🔄 데이터 처리 플로우

### 1. 통합 스케줄러 실행 과정
```
AllDataScheduler.syncAllData()
├── GasPriceService.syncGasPrices()
│   ├── fetchGasPriceData() - API 호출
│   ├── transformApiDataToDbFormat() - 데이터 변환
│   └── batchUpsertGasPrices() - DB 저장
├── FestivalService.syncFestivals()
│   ├── fetchFestivalData() - API 호출
│   ├── transformApiDataToDbFormat() - 데이터 변환
│   └── batchUpsertFestivals() - DB 저장
├── ExhibitionService.syncExhibitions()
│   ├── fetchAllExhibitionData() - 페이지별 API 호출
│   ├── transformApiDataToDbFormat() - 데이터 변환
│   └── batchUpsertExhibitions() - 배치 DB 저장
└── WelfareService.syncWelfareServices()
    ├── fetchWelfareData() - API 호출
    ├── transformApiDataToDbFormat() - 데이터 변환
    └── batchUpsertWelfareServices() - 배치 DB 저장
```

### 2. 에러 처리 전략
- **서비스별 독립 실행**: 하나의 서비스 실패가 다른 서비스에 영향 없음
- **상세 에러 로깅**: 각 서비스별 에러 메시지 및 스택 추적
- **결과 집계**: 성공/실패 결과를 종합하여 리포트 생성

### 3. 성능 최적화
- **배치 처리**: 대량 데이터는 100개씩 배치로 처리
- **API 부하 방지**: 페이지네이션 간 500ms 딜레이
- **DB 부하 분산**: 배치 간 200ms 딜레이

## 🌐 API 엔드포인트 (Cafe24 호스팅)

### GET /
기본 상태 정보 조회
```json
{
  "message": "제주도 통합 데이터 수집 스케줄러",
  "status": "running",
  "timestamp": "2024-01-26T10:30:00.000Z",
  "scheduler": {
    "isRunning": false,
    "scheduleCron": "5 2,8,14,20 * * *",
    "nextRun": "2024-01-26T14:05:00.000Z"
  }
}
```

### GET /status
스케줄러 상태 상세 조회
```json
{
  "isRunning": false,
  "scheduleCron": "5 2,8,14,20 * * *",
  "nextRun": "2024-01-26T14:05:00.000Z"
}
```

### POST /run
수동 실행 트리거
```json
// 성공 응답
{
  "success": true,
  "message": "수동 실행 완료",
  "result": {
    "gasPrice": { "processedCount": 150 },
    "festival": { "processedCount": 25 },
    "exhibition": { "processedCount": 80 },
    "welfare": { "processedCount": 120 },
    "errors": []
  }
}

// 실패 응답
{
  "success": false,
  "message": "수동 실행 실패",
  "error": "Scheduler not initialized"
}
```

## 📝 로깅 시스템

### 로그 레벨 및 내용
1. **INFO**: 정상 실행 과정
   - 스케줄러 시작/종료
   - API 호출 성공
   - 데이터 처리 진행상황

2. **ERROR**: 오류 상황
   - API 호출 실패
   - 데이터베이스 연결 오류
   - 데이터 변환 실패

3. **WARN**: 주의 상황
   - 유효하지 않은 날짜 데이터
   - API 응답 지연

### 로그 출력 예시
```
=== 제주도 통합 데이터 수집 스케줄러 시작 ===
Node.js 버전: v14.21.3
시작 시간: 2024-01-26T02:05:00.000Z

[2024-01-26T02:05:00.000Z] 스케줄된 통합 데이터 동기화 시작

--- 주유소 가격 데이터 동기화 시작 ---
제주도 주유소 가격 API 호출 중...
API 응답 성공: 150개의 주유소 정보 수신
150개의 주유소 데이터 저장 중...
150개의 주유소 데이터 저장 완료
--- 주유소 가격 데이터 동기화 완료 ---

=== 통합 데이터 동기화 결과 요약 ===
주유소 가격: 150개 처리 완료
축제/행사: 25개 처리 완료
전시회: 80개 처리 완료
복지서비스: 120개 처리 완료
=== 통합 데이터 동기화 결과 요약 완료 ===

[2024-01-26T02:07:30.000Z] 스케줄된 통합 데이터 동기화 완료
```

## 🚀 Cafe24 호스팅 배포

### 1. 배포 환경 특징
- **메인 파일**: `web.js` (Cafe24 요구사항)
- **포트**: 8001 (Cafe24 기본 포트)
- **실행 방식**: HTTP 서버 + 백그라운드 스케줄러
- **Node.js 버전**: 14.x (engines 필드에 명시)

### 2. 배포 과정
```bash
# 1. Git 저장소 초기화 (최초 1회)
git init
git remote <NAME_EMAIL>:akapwhddb_grapapi

# 2. 코드 커밋 및 푸시
git add .
git commit -m "Deploy GRAP API"
git push origin master

# 3. Cafe24에서 자동 배포 및 실행
# npm start 명령어로 web.js 실행
```

### 3. 배포 후 확인사항
- HTTP 서버 정상 동작 확인: `GET /`
- 스케줄러 상태 확인: `GET /status`
- 수동 실행 테스트: `POST /run`

## 🔧 개발 및 디버깅

### 개발 도구
```bash
# 통합 스케줄러 테스트
node test-scheduler.js

# API 응답 형식 디버깅
node debug-api.js

# 날짜 형식 확인
node check-date-format.js
```

### 디버깅 팁
1. **API 응답 확인**: `debug-api.js`로 각 API의 응답 형식 검증
2. **날짜 데이터 검증**: `check-date-format.js`로 타임스탬프 변환 확인
3. **개별 서비스 테스트**: 각 서비스 클래스를 독립적으로 테스트
4. **로그 분석**: 상세한 로그를 통한 문제점 추적

### 일반적인 문제 해결
1. **API 호출 실패**
   - 네트워크 연결 확인
   - API 엔드포인트 URL 검증
   - 타임아웃 설정 조정

2. **데이터베이스 연결 오류**
   - Supabase 키 및 URL 확인
   - 네트워크 방화벽 설정 확인

3. **날짜 변환 오류**
   - API 응답의 날짜 형식 확인
   - 타임스탬프 범위 검증 (1900-2100년)

## 📊 모니터링 및 운영

### 성능 지표
- **API 응답 시간**: 각 서비스별 API 호출 소요 시간
- **데이터 처리량**: 서비스별 처리된 레코드 수
- **에러율**: 전체 실행 대비 실패한 서비스 비율
- **스케줄 준수율**: 정해진 시간에 정상 실행된 비율

### 운영 체크리스트
- [ ] 일일 로그 확인
- [ ] 데이터베이스 용량 모니터링
- [ ] API 응답 시간 추이 확인
- [ ] 에러 발생 패턴 분석
- [ ] 스케줄 실행 이력 검토

## ⚠️ 주의사항 및 제한사항

### 보안 관련
1. **API 키 관리**: Supabase 키가 코드에 하드코딩되어 있음
   - 프로덕션 환경에서는 환경변수 사용 권장
   - 정기적인 키 로테이션 필요

2. **접근 제어**: 현재 인증 없이 모든 엔드포인트 접근 가능
   - 필요시 API 키 또는 JWT 인증 추가 고려

### 성능 관련
1. **API 호출 제한**: 제주도 공공 API의 호출 제한 준수 필요
2. **메모리 사용량**: 대량 데이터 처리 시 메모리 사용량 모니터링
3. **동시 실행 방지**: 현재 단일 인스턴스 기준으로 설계됨

### 데이터 관련
1. **데이터 정합성**: API 응답 형식 변경 시 파싱 로직 업데이트 필요
2. **중복 데이터**: upsert 로직으로 중복 방지하지만 API ID 변경 시 중복 가능
3. **데이터 보존**: 현재 무제한 데이터 누적, 정기적 정리 정책 필요

## 🔮 향후 개선 계획

### 단기 개선사항
- [ ] 환경변수 기반 설정 관리
- [ ] API 인증 시스템 추가
- [ ] 상세 모니터링 대시보드
- [ ] 데이터 검증 로직 강화

### 중기 개선사항
- [ ] 마이크로서비스 아키텍처 전환
- [ ] Redis 기반 캐싱 시스템
- [ ] 웹훅 기반 실시간 알림
- [ ] 데이터 백업 및 복구 시스템

### 장기 개선사항
- [ ] 머신러닝 기반 데이터 품질 관리
- [ ] 실시간 스트리밍 데이터 처리
- [ ] 다중 지역 데이터 수집 확장
- [ ] GraphQL API 제공

## 📞 지원 및 문의

### 개발팀 정보
- **Repository**: https://github.com/amaham1/grap_api.git
- **Hosting**: Cafe24 Node.js 호스팅
- **Database**: Supabase PostgreSQL

### 기술 지원
- 버그 리포트: GitHub Issues 활용
- 기능 요청: GitHub Discussions 활용
- 긴급 문의: 프로젝트 관리자 연락

---

**마지막 업데이트**: 2024년 1월 26일
**버전**: 1.0.0
**라이선스**: ISC
