const axios = require('axios');
const { supabase } = require('../config/database');

// API 설정 상수
const JEJU_EXHIBITION_API_URL = 'http://www.jeju.go.kr/rest/JejuExhibitionService/getJejucultureExhibitionList';

class ExhibitionService {
  constructor() {
    this.apiUrl = JEJU_EXHIBITION_API_URL;
  }

  /**
   * 제주도 전시회 API에서 페이지별 데이터를 가져옵니다.
   */
  async fetchExhibitionDataByPage(page = 1) {
    try {
      console.log(`제주도 전시회 API 호출 중... (페이지: ${page})`);
      
      const response = await axios.get(this.apiUrl, {
        params: {
          page: page,
          pageSize: 500
        },
        timeout: 30000 // 30초 타임아웃
      });

      // JSON 응답 처리
      const result = response.data;
      
      if (result.resultCode !== '00') {
        throw new Error(`API 호출 실패: ${result.resultMsg}`);
      }

      const items = result.items || [];
      console.log(`페이지 ${page}: ${items.length}개의 전시회 정보 수신`);
      
      return items;
    } catch (error) {
      console.error(`페이지 ${page} API 호출 중 오류 발생:`, error.message);
      throw error;
    }
  }

  /**
   * 모든 페이지의 전시회 데이터를 가져옵니다.
   */
  async fetchAllExhibitionData() {
    try {
      console.log('제주도 전시회 API 전체 데이터 수집 시작...');
      
      let allData = [];
      let page = 1;
      let hasMoreData = true;

      while (hasMoreData) {
        const pageData = await this.fetchExhibitionDataByPage(page);
        
        if (pageData.length === 0) {
          hasMoreData = false;
        } else {
          allData = allData.concat(pageData);
          page++;
          
          // API 부하 방지를 위한 딜레이
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      console.log(`전체 ${allData.length}개의 전시회 정보 수집 완료`);
      return allData;
    } catch (error) {
      console.error('전체 데이터 수집 중 오류 발생:', error.message);
      throw error;
    }
  }

  /**
   * API 데이터를 DB 스키마에 맞게 변환합니다.
   * @param {Array} apiData - API에서 받은 원본 데이터
   * @param {Array} existingData - 기존 DB에 있는 데이터 (is_exposed 값 유지용)
   */
  transformApiDataToDbFormat(apiData, existingData = []) {
    const currentTime = new Date().toISOString();

    // 기존 데이터를 original_api_id로 매핑
    const existingDataMap = new Map();
    existingData.forEach(item => {
      if (item.original_api_id) {
        existingDataMap.set(item.original_api_id, item);
      }
    });

    return apiData.map(item => {
      // 날짜 변환 함수 - 타임스탬프를 ISO 문자열로 변환
      const convertTimestamp = (timestamp) => {
        if (!timestamp) return null;
        try {
          const date = new Date(timestamp);
          // 유효한 날짜인지 확인 (1900년 이후, 2100년 이전)
          if (date.getFullYear() < 1900 || date.getFullYear() > 2100) {
            console.warn(`유효하지 않은 날짜 범위: ${timestamp} -> ${date}`);
            return null;
          }
          return date.toISOString();
        } catch (error) {
          console.warn(`날짜 변환 실패: ${timestamp}`, error.message);
          return null;
        }
      };

      const originalApiId = item.seq || null;
      const existingItem = existingDataMap.get(originalApiId);

      return {
        original_api_id: originalApiId,
        title: item.title || null,
        category_name: item.categoryName || null,
        cover_image_url: item.cover || null,
        start_date: convertTimestamp(item.start),
        end_date: convertTimestamp(item.end),
        time_info: item.hour || null,
        pay_info: item.pay || null,
        location_name: item.locNames || null,
        organizer_info: item.owner || null,
        tel_number: item.tel || null,
        status_info: item.stat || null,
        division_name: item.divName || null,
        api_raw_data: item,
        // 기존 데이터가 있으면 is_exposed 값을 유지, 없으면 false로 설정
        is_exposed: existingItem ? existingItem.is_exposed : false,
        admin_memo: existingItem ? existingItem.admin_memo : null,
        fetched_at: currentTime,
        updated_at: currentTime
      };
    });
  }

  /**
   * 여러 전시회 데이터를 배치로 upsert합니다.
   */
  async batchUpsertExhibitions(exhibitionsData) {
    try {
      console.log(`${exhibitionsData.length}개의 전시회 데이터 저장 중...`);
      
      // 배치 크기를 100개로 제한하여 DB 부하 감소
      const batchSize = 100;
      let processedCount = 0;

      for (let i = 0; i < exhibitionsData.length; i += batchSize) {
        const batch = exhibitionsData.slice(i, i + batchSize);
        
        // upsert 시 updated_at을 현재 시간으로 업데이트
        const batchWithUpdatedTime = batch.map(item => ({
          ...item,
          updated_at: new Date().toISOString()
        }));
        
        const { data, error } = await supabase
          .from('exhibitions')
          .upsert(batchWithUpdatedTime, {
            onConflict: 'original_api_id'
          });

        if (error) {
          throw error;
        }

        processedCount += batch.length;
        console.log(`${processedCount}/${exhibitionsData.length}개 처리 완료`);
        
        // 배치 간 딜레이
        if (i + batchSize < exhibitionsData.length) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }

      console.log(`${exhibitionsData.length}개의 전시회 데이터 저장 완료`);
      return { processedCount };
    } catch (error) {
      console.error('배치 데이터 저장 실패:', error.message);
      throw error;
    }
  }

  /**
   * 기존 전시회 데이터를 조회합니다.
   */
  async getExistingExhibitions() {
    try {
      const { data, error } = await supabase
        .from('exhibitions')
        .select('original_api_id, is_exposed, admin_memo');

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('기존 전시회 데이터 조회 실패:', error.message);
      return []; // 조회 실패 시 빈 배열 반환하여 새 데이터로 처리
    }
  }

  /**
   * 전체 프로세스를 실행합니다: API 호출 → 기존 데이터 조회 → 데이터 변환 → DB 저장
   */
  async syncExhibitions() {
    try {
      console.log('=== 전시회 데이터 동기화 시작 ===');
      const startTime = new Date();

      // 1. API에서 모든 페이지 데이터 가져오기
      const apiData = await this.fetchAllExhibitionData();

      if (!apiData || apiData.length === 0) {
        console.log('API에서 받은 데이터가 없습니다.');
        return;
      }

      // 2. 기존 데이터 조회 (is_exposed 값 유지용)
      console.log('기존 전시회 데이터 조회 중...');
      const existingData = await this.getExistingExhibitions();
      console.log(`기존 데이터 ${existingData.length}개 조회 완료`);

      // 3. 데이터 변환 (기존 is_exposed 값 유지)
      const dbData = this.transformApiDataToDbFormat(apiData, existingData);

      // 4. DB에 저장
      await this.batchUpsertExhibitions(dbData);

      const endTime = new Date();
      const duration = (endTime - startTime) / 1000;

      console.log(`=== 전시회 데이터 동기화 완료 (소요시간: ${duration}초) ===`);

      return {
        success: true,
        processedCount: dbData.length,
        duration: duration
      };
    } catch (error) {
      console.error('전시회 데이터 동기화 실패:', error.message);
      throw error;
    }
  }
}

module.exports = ExhibitionService;