/**
 * is_exposed 컬럼 값 유지 테스트 스크립트
 * 
 * 이 스크립트는 수정된 서비스들이 기존 데이터의 is_exposed 값을 
 * 올바르게 유지하는지 테스트합니다.
 */

const FestivalService = require('./src/services/festivalService');
const ExhibitionService = require('./src/services/exhibitionService');
const WelfareService = require('./src/services/welfareService');

async function testIsExposedPreservation() {
  console.log('=== is_exposed 컬럼 값 유지 테스트 시작 ===\n');

  try {
    // 1. FestivalService 테스트
    console.log('1. FestivalService 테스트');
    const festivalService = new FestivalService();
    
    // 모의 API 데이터
    const mockFestivalApiData = [
      { seq: 'test_festival_1', title: '테스트 축제 1', contents: '테스트 내용' },
      { seq: 'test_festival_2', title: '테스트 축제 2', contents: '테스트 내용' }
    ];
    
    // 모의 기존 데이터 (is_exposed가 true인 데이터 포함)
    const mockExistingFestivalData = [
      { original_api_id: 'test_festival_1', is_exposed: true, admin_memo: '기존 메모' }
    ];
    
    const transformedFestivalData = festivalService.transformApiDataToDbFormat(
      mockFestivalApiData, 
      mockExistingFestivalData
    );
    
    console.log('변환된 축제 데이터:');
    transformedFestivalData.forEach(item => {
      console.log(`- ID: ${item.original_api_id}, is_exposed: ${item.is_exposed}, admin_memo: ${item.admin_memo}`);
    });
    
    // 검증
    const existingItem = transformedFestivalData.find(item => item.original_api_id === 'test_festival_1');
    const newItem = transformedFestivalData.find(item => item.original_api_id === 'test_festival_2');
    
    if (existingItem.is_exposed === true && existingItem.admin_memo === '기존 메모') {
      console.log('✅ 기존 데이터의 is_exposed 값이 올바르게 유지됨');
    } else {
      console.log('❌ 기존 데이터의 is_exposed 값이 유지되지 않음');
    }
    
    if (newItem.is_exposed === false && newItem.admin_memo === null) {
      console.log('✅ 새 데이터의 is_exposed 값이 올바르게 설정됨');
    } else {
      console.log('❌ 새 데이터의 is_exposed 값이 올바르지 않음');
    }
    
    console.log('');

    // 2. ExhibitionService 테스트
    console.log('2. ExhibitionService 테스트');
    const exhibitionService = new ExhibitionService();
    
    const mockExhibitionApiData = [
      { seq: 'test_exhibition_1', title: '테스트 전시 1', categoryName: '문화' },
      { seq: 'test_exhibition_2', title: '테스트 전시 2', categoryName: '예술' }
    ];
    
    const mockExistingExhibitionData = [
      { original_api_id: 'test_exhibition_1', is_exposed: true, admin_memo: '전시 메모' }
    ];
    
    const transformedExhibitionData = exhibitionService.transformApiDataToDbFormat(
      mockExhibitionApiData, 
      mockExistingExhibitionData
    );
    
    console.log('변환된 전시 데이터:');
    transformedExhibitionData.forEach(item => {
      console.log(`- ID: ${item.original_api_id}, is_exposed: ${item.is_exposed}, admin_memo: ${item.admin_memo}`);
    });
    
    // 검증
    const existingExhibition = transformedExhibitionData.find(item => item.original_api_id === 'test_exhibition_1');
    const newExhibition = transformedExhibitionData.find(item => item.original_api_id === 'test_exhibition_2');
    
    if (existingExhibition.is_exposed === true && existingExhibition.admin_memo === '전시 메모') {
      console.log('✅ 기존 전시 데이터의 is_exposed 값이 올바르게 유지됨');
    } else {
      console.log('❌ 기존 전시 데이터의 is_exposed 값이 유지되지 않음');
    }
    
    if (newExhibition.is_exposed === false && newExhibition.admin_memo === null) {
      console.log('✅ 새 전시 데이터의 is_exposed 값이 올바르게 설정됨');
    } else {
      console.log('❌ 새 전시 데이터의 is_exposed 값이 올바르지 않음');
    }
    
    console.log('');

    // 3. WelfareService 테스트
    console.log('3. WelfareService 테스트');
    const welfareService = new WelfareService();
    
    const mockWelfareApiData = [
      { seq: 'test_welfare_1', name: '테스트 복지 1', allLoc: true },
      { seq: 'test_welfare_2', name: '테스트 복지 2', jejuLoc: true }
    ];
    
    const mockExistingWelfareData = [
      { original_api_id: 'test_welfare_1', is_exposed: true, admin_memo: '복지 메모' }
    ];
    
    const transformedWelfareData = welfareService.transformApiDataToDbFormat(
      mockWelfareApiData, 
      mockExistingWelfareData
    );
    
    console.log('변환된 복지 데이터:');
    transformedWelfareData.forEach(item => {
      console.log(`- ID: ${item.original_api_id}, is_exposed: ${item.is_exposed}, admin_memo: ${item.admin_memo}`);
    });
    
    // 검증
    const existingWelfare = transformedWelfareData.find(item => item.original_api_id === 'test_welfare_1');
    const newWelfare = transformedWelfareData.find(item => item.original_api_id === 'test_welfare_2');
    
    if (existingWelfare.is_exposed === true && existingWelfare.admin_memo === '복지 메모') {
      console.log('✅ 기존 복지 데이터의 is_exposed 값이 올바르게 유지됨');
    } else {
      console.log('❌ 기존 복지 데이터의 is_exposed 값이 유지되지 않음');
    }
    
    if (newWelfare.is_exposed === false && newWelfare.admin_memo === null) {
      console.log('✅ 새 복지 데이터의 is_exposed 값이 올바르게 설정됨');
    } else {
      console.log('❌ 새 복지 데이터의 is_exposed 값이 올바르지 않음');
    }

    console.log('\n=== 테스트 완료 ===');
    
  } catch (error) {
    console.error('테스트 중 오류 발생:', error.message);
  }
}

// 테스트 실행
if (require.main === module) {
  testIsExposedPreservation();
}

module.exports = { testIsExposedPreservation };
